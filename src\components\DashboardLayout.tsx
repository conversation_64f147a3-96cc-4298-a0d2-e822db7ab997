'use client'

import NavigationSidebar from './NavigationSidebar'
import { NotificationContainer } from './AnimatedNotification'
import SmartSearch from './search/SmartSearch'
import NavigationHistory from './navigation/NavigationHistory'
import { Bell, Search, User, Calendar, Clock, ChevronDown } from 'lucide-react'
import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { useHydrationSafe } from '@/hooks/useHydrationSafe'

interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  actions?: React.ReactNode
}

// Page titles and subtitles mapping
const pageInfo: Record<string, { title: string; subtitle: string }> = {
  '/dashboard': { title: 'Dashboard', subtitle: 'Overview & analytics' },
  '/products': { title: 'Products', subtitle: 'Manage your store inventory • 5 products' },
  '/customers': { title: 'Customers', subtitle: 'Customer database' },
  '/debts': { title: 'Debts', subtitle: 'Track customer debts' },
  '/payments': { title: 'Payments', subtitle: 'Payment records' },
  '/reports': { title: 'Reports', subtitle: 'Business insights' },
}

export default function DashboardLayout({
  children,
  title,
  subtitle,
  actions
}: DashboardLayoutProps) {
  const pathname = usePathname()
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const isHydrated = useHydrationSafe()

  // Get current page info if not provided
  const currentPageInfo = pageInfo[pathname] || { title: 'Dashboard', subtitle: 'Overview & analytics' }
  const pageTitle = title || currentPageInfo.title
  const pageSubtitle = subtitle || currentPageInfo.subtitle

  // Initialize client-side only
  useEffect(() => {
    setCurrentTime(new Date())

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [pathname])

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('[data-dropdown]')) {
        setShowNotifications(false)
        setShowProfile(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const formatTime = (date: Date | null) => {
    if (!date || !isHydrated) return '--:--'
    try {
      // Use consistent formatting to avoid hydration mismatches
      const hours = date.getHours()
      const minutes = date.getMinutes()
      const ampm = hours >= 12 ? 'PM' : 'AM'
      const displayHours = hours % 12 || 12
      const displayMinutes = minutes.toString().padStart(2, '0')
      return `${displayHours}:${displayMinutes} ${ampm}`
    } catch {
      return '--:--'
    }
  }

  const formatDate = (date: Date | null) => {
    if (!date || !isHydrated) return 'Loading...'
    try {
      // Use consistent formatting to avoid hydration mismatches
      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
      const months = ['January', 'February', 'March', 'April', 'May', 'June',
                     'July', 'August', 'September', 'October', 'November', 'December']

      const dayName = days[date.getDay()]
      const monthName = months[date.getMonth()]
      const dayNumber = date.getDate()
      const year = date.getFullYear()

      return `${dayName}, ${monthName} ${dayNumber}, ${year}`
    } catch {
      return 'Loading...'
    }
  }

  return (
    <div className="dashboard-layout min-h-screen bg-gray-50" suppressHydrationWarning>
      {/* Navigation Sidebar - Hydration Safe */}
      <NavigationSidebar
        isCollapsed={isCollapsed}
        onToggleCollapse={() => setIsCollapsed(!isCollapsed)}
        isMobileMenuOpen={isMobileMenuOpen}
        onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      />

      {/* Professional Header - Enhanced Design */}
      <header className={`dashboard-header ${isCollapsed ? 'collapsed' : ''} bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-sm`}>
          <div className="px-4 lg:px-6 h-full w-full">
            <div className="flex items-center justify-between h-full">
              {/* Left Section - Controls & Title */}
              <div className="flex items-center space-x-4 min-w-0 flex-1">
                {/* Mobile Menu Button - Professional Design */}
                <button
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className="lg:hidden p-2.5 rounded-xl hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 z-50 relative group shadow-sm hover:shadow-md"
                  title={isMobileMenuOpen ? "Close menu" : "Open menu"}
                  aria-label={isMobileMenuOpen ? "Close navigation menu" : "Open navigation menu"}
                >
                  {isMobileMenuOpen ? (
                    <svg className="h-5 w-5 text-gray-600 group-hover:text-gray-800 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-gray-600 group-hover:text-gray-800 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  )}
                </button>

                {/* Desktop Sidebar Toggle Button - Professional Design */}
                <button
                  onClick={() => setIsCollapsed(!isCollapsed)}
                  className="hidden lg:flex items-center justify-center p-2.5 rounded-xl hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 group shadow-sm hover:shadow-md"
                  title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                >
                  {isCollapsed ? (
                    <svg className="h-5 w-5 text-gray-600 group-hover:text-gray-800 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h8M4 18h16" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-gray-600 group-hover:text-gray-800 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  )}
                </button>

                {/* Enhanced Page Title Section */}
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-3">
                    <div className="min-w-0">
                      <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate tracking-tight">{pageTitle}</h1>
                      <p className="text-sm text-gray-600 truncate mt-0.5 font-medium">{pageSubtitle}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Center Section - Smart Search */}
              <div className="flex-1 max-w-2xl mx-4 hidden md:block">
                <SmartSearch
                  placeholder="Search products, customers, or transactions..."
                  showFilters={true}
                  className="w-full"
                />
              </div>

              {/* Professional Right Section */}
              <div className="flex items-center space-x-3">
                {/* Enhanced Time & Date Display - Hydration Safe */}
                <div className="hidden lg:flex items-center space-x-6 text-sm text-gray-600 mr-6 bg-gray-50 px-4 py-2 rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-green-600" />
                    <span className="font-semibold text-gray-800" suppressHydrationWarning>
                      {isHydrated ? formatTime(currentTime) : '--:--'}
                    </span>
                  </div>
                  <div className="w-px h-4 bg-gray-300"></div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-green-600" />
                    <span className="font-medium" suppressHydrationWarning>
                      {isHydrated ? formatDate(currentTime) : 'Loading...'}
                    </span>
                  </div>
                </div>

                {/* Mobile Search Button */}
                <button className="md:hidden p-2.5 rounded-xl hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 shadow-sm hover:shadow-md">
                  <Search className="h-5 w-5 text-gray-600" />
                </button>

                {/* Navigation History */}
                <NavigationHistory />

                {/* Professional Notifications */}
                <div className="relative" data-dropdown>
                  <button
                    onClick={() => setShowNotifications(!showNotifications)}
                    className="relative p-2.5 rounded-xl hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 shadow-sm hover:shadow-md group"
                    title="Notifications"
                  >
                    <Bell className="h-5 w-5 text-gray-600 group-hover:text-gray-800 transition-colors" />
                    <span className="absolute top-1.5 right-1.5 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-white shadow-sm animate-pulse"></span>
                  </button>

                  {/* Enhanced Notifications Dropdown */}
                  {showNotifications && (
                    <div className="absolute right-0 mt-3 w-80 bg-white rounded-2xl shadow-2xl border border-gray-200 z-50 backdrop-blur-sm">
                      <div className="p-5 border-b border-gray-100">
                        <div className="flex items-center justify-between">
                          <h3 className="font-bold text-gray-900 text-lg">Notifications</h3>
                          <span className="bg-red-100 text-red-600 text-xs font-semibold px-2 py-1 rounded-full">2 new</span>
                        </div>
                      </div>
                      <div className="p-4 space-y-4 max-h-64 overflow-y-auto">
                        <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-xl border border-blue-200">
                          <div className="w-3 h-3 bg-blue-500 rounded-full mt-2 shadow-sm"></div>
                          <div className="flex-1">
                            <p className="text-sm font-semibold text-gray-900">Low Stock Alert</p>
                            <p className="text-xs text-gray-600 mt-1">3 products are running low</p>
                            <p className="text-xs text-blue-600 mt-2 font-medium">2 minutes ago</p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-xl border border-green-200">
                          <div className="w-3 h-3 bg-green-500 rounded-full mt-2 shadow-sm"></div>
                          <div className="flex-1">
                            <p className="text-sm font-semibold text-gray-900">Payment Received</p>
                            <p className="text-xs text-gray-600 mt-1">₱250 from Juan Dela Cruz</p>
                            <p className="text-xs text-green-600 mt-2 font-medium">5 minutes ago</p>
                          </div>
                        </div>
                      </div>
                      <div className="p-4 border-t border-gray-100">
                        <button className="w-full text-sm text-green-600 hover:text-green-700 font-semibold py-2 px-4 bg-green-50 hover:bg-green-100 rounded-xl transition-colors">
                          View all notifications
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Professional Profile */}
                <div className="relative" data-dropdown>
                  <button
                    onClick={() => setShowProfile(!showProfile)}
                    className="flex items-center space-x-3 p-2 rounded-xl hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 shadow-sm hover:shadow-md group"
                  >
                    <div className="w-9 h-9 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg">
                      <User className="h-5 w-5 text-white" />
                    </div>
                    <div className="hidden sm:block text-left">
                      <p className="text-sm font-semibold text-gray-900">Store Owner</p>
                      <p className="text-xs text-gray-500 font-medium">Administrator</p>
                    </div>
                    <ChevronDown className="h-4 w-4 text-gray-500 group-hover:text-gray-700 transition-colors" />
                  </button>

                  {/* Enhanced Profile Dropdown */}
                  {showProfile && (
                    <div className="absolute right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-gray-200 z-50 backdrop-blur-sm">
                      <div className="p-5 border-b border-gray-100">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg">
                            <User className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <p className="font-bold text-gray-900">Store Owner</p>
                            <p className="text-sm text-gray-500"><EMAIL></p>
                            <p className="text-xs text-green-600 font-semibold mt-1">Administrator</p>
                          </div>
                        </div>
                      </div>
                      <div className="p-3">
                        <button className="w-full flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 rounded-xl transition-all duration-200 group">
                          <User className="h-4 w-4 mr-3 text-gray-500 group-hover:text-gray-700" />
                          <span className="font-medium">Profile Settings</span>
                        </button>
                        <button className="w-full flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 rounded-xl transition-all duration-200 group">
                          <svg className="h-4 w-4 mr-3 text-gray-500 group-hover:text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          <span className="font-medium">Account Settings</span>
                        </button>
                      </div>
                      <div className="p-3 border-t border-gray-100">
                        <button className="w-full flex items-center px-4 py-3 text-sm text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200 font-medium">
                          <svg className="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                          </svg>
                          Sign Out
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Custom Actions */}
                {actions && (
                  <div className="flex items-center space-x-2">
                    {actions}
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

      {/* Main Content Area - YouTube Style */}
      <div className={`main-content-area pt-16 ${isCollapsed ? 'collapsed' : ''}`}>
        {/* Main Content */}
        <main className="p-4 lg:p-6 min-h-[calc(100vh-4rem)] pb-20 w-full">
          <div className="w-full max-w-full overflow-x-hidden">
            {/* Content Container */}
            <div className="w-full">
              {children}
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-white border-t border-gray-200 px-4 lg:px-6 py-4 mt-auto">
          <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <span>&copy; 2024 Caparan Tindahan</span>
              <span className="hidden md:inline">•</span>
              <span className="hidden md:inline">Version 1.0.0</span>
            </div>
            <div className="flex items-center space-x-4 mt-2 md:mt-0">
              <span className="flex items-center">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                System Online
              </span>
              <span className="hidden md:inline">•</span>
              <span className="hidden md:inline">Last backup: Today 3:00 AM</span>
            </div>
          </div>
        </footer>
      </div>

      {/* Notification Container */}
      <NotificationContainer />
    </div>
  )
}
