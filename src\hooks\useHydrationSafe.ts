import { useState, useEffect } from 'react'

/**
 * Hook to handle hydration-safe state management
 * Prevents hydration mismatches by ensuring consistent initial state
 */
export function useHydrationSafe() {
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    setIsHydrated(true)
  }, [])

  return isHydrated
}

/**
 * Hook for hydration-safe localStorage access
 * Returns null during SSR and actual value after hydration
 */
export function useLocalStorage<T>(key: string, defaultValue: T) {
  const [value, setValue] = useState<T>(defaultValue)
  const [isLoaded, setIsLoaded] = useState(false)
  const isHydrated = useHydrationSafe()

  useEffect(() => {
    if (!isHydrated) return

    try {
      const item = localStorage.getItem(key)
      if (item) {
        setValue(JSON.parse(item))
      }
    } catch (error) {
      console.warn(`Error loading localStorage key "${key}":`, error)
    } finally {
      setIsLoaded(true)
    }
  }, [key, isHydrated])

  const setStoredValue = (newValue: T) => {
    try {
      setValue(newValue)
      if (isHydrated) {
        localStorage.setItem(key, JSON.stringify(newValue))
      }
    } catch (error) {
      console.warn(`Error saving to localStorage key "${key}":`, error)
    }
  }

  return [value, setStoredValue, isLoaded] as const
}

/**
 * Hook for hydration-safe date formatting
 * Returns placeholder during SSR and formatted date after hydration
 */
export function useHydrationSafeDate(date: Date | string | null, options?: Intl.DateTimeFormatOptions) {
  const isHydrated = useHydrationSafe()

  if (!isHydrated || !date) {
    return 'Loading...'
  }

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return dateObj.toLocaleDateString('en-US', options)
  } catch {
    return 'Invalid date'
  }
}
