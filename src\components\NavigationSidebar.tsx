'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Package,
  Users,
  CreditCard,
  Receipt,
  BarChart3,
  Home,
  Store,
  FileText,
  Settings,
  LogOut
} from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home, description: 'Overview & analytics' },
  { name: 'Analytics', href: '/analytics', icon: BarChart3, description: 'Advanced charts & insights' },
  { name: 'Products', href: '/products', icon: Package, description: 'Manage inventory' },
  { name: 'Customers', href: '/customers', icon: Users, description: 'Customer database' },
  { name: 'Debts', href: '/debts', icon: CreditCard, description: 'Track customer debts' },
  { name: 'Payments', href: '/payments', icon: Receipt, description: 'Payment records' },
  { name: 'Reports', href: '/reports', icon: FileText, description: 'Business reports' },
]

interface NavigationSidebarProps {
  isCollapsed?: boolean
  onToggleCollapse?: () => void
  isMobileMenuOpen?: boolean
  onMobileMenuToggle?: () => void
}

export default function NavigationSidebar({
  isCollapsed = false,
  onToggleCollapse,
  isMobileMenuOpen = false,
  onMobileMenuToggle
}: NavigationSidebarProps) {
  const pathname = usePathname()

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 backdrop-blur-sm"
          onClick={() => onMobileMenuToggle?.()}
        />
      )}

      {/* Navigation Sidebar - Professional Green Theme */}
      <nav 
        className={`
          navigation-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobileMenuOpen ? 'mobile-open' : ''}
          overflow-y-auto flex flex-col shadow-sm lg:shadow-none
        `}
        suppressHydrationWarning
      >
        {/* Professional Header */}
        <div className={`sidebar-header flex items-center justify-between border-b border-gray-100 bg-white ${isCollapsed ? 'p-4' : 'p-5'}`}>
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-br from-green-600 to-green-700 p-2.5 rounded-xl shadow-lg">
                <Store className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900 tracking-tight">
                  Caparan Tindahan
                </h1>
                <p className="text-xs text-gray-500 font-medium">Admin Dashboard</p>
              </div>
            </div>
          )}

          {isCollapsed && (
            <div className="flex items-center justify-center w-full">
              <div className="bg-gradient-to-br from-green-600 to-green-700 p-2.5 rounded-xl shadow-lg">
                <Store className="h-6 w-6 text-white" />
              </div>
            </div>
          )}

          {/* Professional Collapse Toggle - Desktop Only */}
          <button
            onClick={onToggleCollapse}
            className="hidden lg:flex items-center justify-center w-8 h-8 rounded-lg hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 group"
            title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {isCollapsed ? (
              <svg className="h-4 w-4 text-gray-500 group-hover:text-gray-700 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h8M4 18h16" />
              </svg>
            ) : (
              <svg className="h-4 w-4 text-gray-500 group-hover:text-gray-700 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            )}
          </button>
        </div>

        {/* Professional Navigation Links */}
        <div className="flex-1 py-6">
          <ul className={`space-y-2 ${isCollapsed ? 'px-3' : 'px-5'}`}>
            {navigation.map((item) => {
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    onClick={() => onMobileMenuToggle && onMobileMenuToggle()}
                    className={`
                      group flex items-center rounded-xl transition-all duration-200 relative
                      ${isCollapsed ? 'p-3 justify-center' : 'px-4 py-3'}
                      ${isActive
                        ? 'bg-gradient-to-r from-green-50 to-green-100 text-green-700 shadow-sm border border-green-200'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'
                      }
                    `}
                    title={isCollapsed ? item.name : undefined}
                  >
                    <div className={`
                      flex items-center justify-center transition-all duration-200
                      ${isCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-4'}
                      ${isActive ? 'text-green-600 scale-110' : 'text-gray-500 group-hover:text-gray-700 group-hover:scale-105'}
                    `}>
                      <item.icon className="w-full h-full" />
                    </div>

                    {!isCollapsed && (
                      <div className="flex-1 min-w-0">
                        <div className="font-semibold text-sm truncate">{item.name}</div>
                        <div className="text-xs text-gray-500 truncate mt-0.5">{item.description}</div>
                      </div>
                    )}

                    {isActive && !isCollapsed && (
                      <div className="w-2 h-2 bg-green-600 rounded-full shadow-sm"></div>
                    )}

                    {/* Professional hover effect */}
                    {!isActive && (
                      <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-green-100 opacity-0 group-hover:opacity-30 transition-opacity duration-200 rounded-xl"></div>
                    )}
                  </Link>
                </li>
              )
            })}
          </ul>
        </div>

        {/* Professional Footer Section */}
        <div className="border-t border-gray-100 bg-gray-50 p-5">
          <div className="space-y-3">
            <button className={`
              group w-full flex items-center text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-lg transition-all duration-200 shadow-sm hover:shadow-md
              ${isCollapsed ? 'p-3 justify-center' : 'px-4 py-3'}
            `} title={isCollapsed ? "Settings" : undefined}>
              <Settings className={`${isCollapsed ? 'w-5 h-5' : 'w-4 h-4 mr-3'} text-gray-500 group-hover:text-gray-700 transition-colors`} />
              {!isCollapsed && <span className="font-medium">Settings</span>}
            </button>

            <button className={`
              group w-full flex items-center text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200
              ${isCollapsed ? 'p-3 justify-center' : 'px-4 py-3'}
            `} title={isCollapsed ? "Sign Out" : undefined}>
              <LogOut className={`${isCollapsed ? 'w-5 h-5' : 'w-4 h-4 mr-3'} transition-colors`} />
              {!isCollapsed && <span className="font-medium">Sign Out</span>}
            </button>
          </div>

          {/* Enhanced Store Info - Only show when not collapsed */}
          {!isCollapsed && (
            <div className="mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
              <div className="text-xs font-semibold text-gray-900 mb-2">Store Status</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  <span className="text-xs text-gray-700 font-medium">Online & Active</span>
                </div>
                <div className="text-xs text-green-600 font-semibold">24/7</div>
              </div>
            </div>
          )}
        </div>
      </nav>
    </>
  )
}
